"""
Test cases for default edge functionality in chatbot services.

This module tests the default edge handling in the _select_edge_for_input method
to ensure that edges with sourceHandle='default' are properly used as fallback edges.
"""

import pytest
from unittest.mock import MagicMock
from app.services.message_event_listener import MessageEventListener


class TestDefaultEdgeFunctionality:
    """Test cases for default edge functionality"""

    def setup_method(self):
        """Set up test fixtures"""
        self.listener = MessageEventListener()

    def test_default_edge_for_empty_input_question_node(self):
        """Test that default edge is used for empty input on question nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "question"
        mock_node.data = {
            "options": [
                {"text": "Option 1", "name": "1"},
                {"text": "Option 2", "name": "2"}
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="1", target_node="option1_node"),
            MagicMock(source_handle="2", target_node="option2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test empty input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, ""
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_empty_input_list_node(self):
        """Test that default edge is used for empty input on list nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.data = {
            "sections": [
                {
                    "title": "Options",
                    "rows": [
                        {"id": "opt-1", "text": "First Choice"},
                        {"id": "opt-2", "text": "Second Choice"}
                    ]
                }
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="opt-1", target_node="first_node"),
            MagicMock(source_handle="opt-2", target_node="second_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test empty input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, ""
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_empty_input_buttons_node(self):
        """Test that default edge is used for empty input on buttons nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "buttons"
        mock_node.data = {
            "buttons": [
                {"id": "btn-1", "text": "Button 1"},
                {"id": "btn-2", "text": "Button 2"}
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="btn-1", target_node="button1_node"),
            MagicMock(source_handle="btn-2", target_node="button2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test empty input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, ""
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_media_question_node(self):
        """Test that default edge is used for media question nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "question"
        mock_node.data = {
            "isMedia": True,
            "options": []  # No options for free text
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="default", target_node="default_node"),
            MagicMock(source_handle="continue", target_node="continue_node")
        ]

        # Test empty input (media upload)
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, ""
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_unmatched_input_question_node(self):
        """Test that default edge is used for unmatched input on question nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "question"
        mock_node.data = {
            "options": [
                {"text": "Option 1", "name": "1"},
                {"text": "Option 2", "name": "2"}
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="1", target_node="option1_node"),
            MagicMock(source_handle="2", target_node="option2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test unmatched input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_option"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_unmatched_input_list_node(self):
        """Test that default edge is used for unmatched input on list nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.data = {
            "sections": [
                {
                    "title": "Options",
                    "rows": [
                        {"id": "opt-1", "text": "First Choice"},
                        {"id": "opt-2", "text": "Second Choice"}
                    ]
                }
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="opt-1", target_node="first_node"),
            MagicMock(source_handle="opt-2", target_node="second_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test unmatched input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_choice"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_unmatched_input_buttons_node(self):
        """Test that default edge is used for unmatched input on buttons nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "buttons"
        mock_node.data = {
            "buttons": [
                {"id": "btn-1", "text": "Button 1"},
                {"id": "btn-2", "text": "Button 2"}
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="btn-1", target_node="button1_node"),
            MagicMock(source_handle="btn-2", target_node="button2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test unmatched input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_button"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_for_free_text_question_node(self):
        """Test that default edge is used for free text question nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "question"
        mock_node.data = {
            "options": []  # No options for free text
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="default", target_node="default_node"),
            MagicMock(source_handle="continue", target_node="continue_node")
        ]

        # Test with any input (free text)
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "any_text_input"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_default_edge_fallback_priority(self):
        """Test that default edge has proper priority in fallback scenarios"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "question"
        mock_node.data = {
            "options": [
                {"text": "Option 1", "name": "1"}
            ]
        }

        # Create mock edges with a default edge and regular edge
        mock_edges = [
            MagicMock(source_handle="1", target_node="option1_node"),
            MagicMock(source_handle="default", target_node="default_node"),
            MagicMock(source_handle="continue", target_node="continue_node")
        ]

        # Test unmatched input - should use default edge, not first edge
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_option"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_no_default_edge_fallback_to_first_edge(self):
        """Test that when no default edge exists, it falls back to first edge"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "buttons"
        mock_node.data = {
            "buttons": [
                {"id": "btn-1", "text": "Button 1"}
            ]
        }

        # Create mock edges without a default edge
        mock_edges = [
            MagicMock(source_handle="btn-1", target_node="button1_node"),
            MagicMock(source_handle="continue", target_node="continue_node")
        ]

        # Test empty input - should fall back to first edge
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, ""
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "button1_node"
        assert selected_edge.source_handle == "btn-1"

    def test_default_edge_for_condition_node(self):
        """Test that default edge is used for unmatched input on condition nodes"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "condition"
        mock_node.data = {
            "conditions": [
                {"name": "cond1", "value": "value1"},
                {"name": "cond2", "value": "value2"}
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="cond1", target_node="condition1_node"),
            MagicMock(source_handle="cond2", target_node="condition2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test unmatched input
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_condition"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"

    def test_invalid_input_triggers_default_edge(self):
        """Test that invalid input triggers default edge when available"""
        # Create mock node
        mock_node = MagicMock()
        mock_node.type = "list"
        mock_node.data = {
            "sections": [
                {
                    "title": "Options",
                    "rows": [
                        {"id": "row-1", "text": "Option 1"},
                        {"id": "row-2", "text": "Option 2"}
                    ]
                }
            ]
        }

        # Create mock edges with a default edge
        mock_edges = [
            MagicMock(source_handle="row-1", target_node="option1_node"),
            MagicMock(source_handle="row-2", target_node="option2_node"),
            MagicMock(source_handle="default", target_node="default_node")
        ]

        # Test invalid input (not matching any row)
        selected_edge = self.listener._select_edge_for_input(
            mock_node, mock_edges, "invalid_random_text"
        )

        assert selected_edge is not None
        assert selected_edge.target_node == "default_node"
        assert selected_edge.source_handle == "default"
